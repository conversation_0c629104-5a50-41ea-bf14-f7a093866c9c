import React, { useState, useEffect, useRef } from 'react';
import { Zap, TrendingUp, Shield, Award, Users, Monitor, BarChart3, Leaf, Settings, CheckCircle, ArrowRight, Sparkles, Target, Globe } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import PageLayout from "@/components/layout/PageLayout";

const OnPremiseSystemsPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedModule, setSelectedModule] = useState<number | null>(null);
  const [countsStarted, setCountsStarted] = useState(false);
  const statsRef = useRef<HTMLDivElement>(null);

  const stats = [
    { label: 'Typical Energy Savings', value: '8-15%', displayValue: '8-15%', icon: TrendingUp, color: 'from-green-500 to-emerald-600' },
    { label: 'Payback Period', value: '1.5 - 2 Years', displayValue: '1.5 - 2 Years', icon: Award, color: 'from-emerald-500 to-teal-600' },
    { label: 'Customers Served', value: '300+', displayValue: '300+', countTo: 300, icon: Users, color: 'from-teal-500 to-green-600' },
    { label: 'Devices Connected', value: '20,000+', displayValue: '20,000+', countTo: 20000, icon: Monitor, color: 'from-green-600 to-emerald-700' }
  ];

  // Count-up animation state
  const [customersCount, setCustomersCount] = useState(0);
  const [devicesCount, setDevicesCount] = useState(0);

  // Intersection Observer for triggering count-up
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !countsStarted) {
          setCountsStarted(true);

          // Animate customers count
          const customersTarget = 300;
          const customersDuration = 2000;
          const customersStartTime = Date.now();

          const animateCustomers = () => {
            const elapsed = Date.now() - customersStartTime;
            const progress = Math.min(elapsed / customersDuration, 1);
            const current = Math.floor(progress * customersTarget);
            setCustomersCount(current);

            if (progress < 1) {
              requestAnimationFrame(animateCustomers);
            }
          };

          // Animate devices count
          const devicesTarget = 20000;
          const devicesDuration = 2500;
          const devicesStartTime = Date.now();

          const animateDevices = () => {
            const elapsed = Date.now() - devicesStartTime;
            const progress = Math.min(elapsed / devicesDuration, 1);
            const current = Math.floor(progress * devicesTarget);
            setDevicesCount(current);

            if (progress < 1) {
              requestAnimationFrame(animateDevices);
            }
          };

          // Start animations
          requestAnimationFrame(animateCustomers);
          requestAnimationFrame(animateDevices);
        }
      },
      { threshold: 0.3 }
    );

    if (statsRef.current) {
      observer.observe(statsRef.current);
    }

    return () => observer.disconnect();
  }, [countsStarted]);

  const addOnModules = [
    {
      title: 'Power Quality Management',
      description: 'Advanced electrical system monitoring with real-time analytics',
      icon: Zap,
      features: ['Real-time power quality monitoring', 'Harmonic analysis', 'Voltage fluctuation tracking', 'Equipment protection alerts']
    },
    {
      title: 'Demand Management',
      description: 'Intelligent peak demand optimization and load control',
      icon: BarChart3,
      features: ['Peak demand forecasting', 'Load shifting strategies', 'Cost optimization', 'Automated load control']
    },
    {
      title: 'Asset Performance',
      description: 'Maximize utility asset performance and energy conservation',
      icon: Settings,
      features: ['Asset health monitoring', 'Performance optimization', 'Energy conservation tracking', 'Maintenance scheduling']
    },
    {
      title: 'ISO 50001 Compliance',
      description: 'Complete energy management standards compliance solution',
      icon: Shield,
      features: ['Compliance documentation', 'Audit trail management', 'Standard reporting', 'Certification support']
    },
    {
      title: 'Sustainability Reporting',
      description: 'Comprehensive carbon footprint and compliance reporting',
      icon: Leaf,
      features: ['Carbon footprint calculation', 'Emission tracking', 'Sustainability metrics', 'Regulatory compliance']
    },
    {
      title: 'Asset Management',
      description: 'Predictive maintenance and asset lifecycle optimization',
      icon: Monitor,
      features: ['Predictive maintenance', 'Asset lifecycle tracking', 'Maintenance scheduling', 'Performance analytics']
    }
  ];

  const coreFeatures = [
    'Real-time energy monitoring and analytics',
    'AI-powered predictive intelligence',
    'Automated energy optimization',
    'Comprehensive reporting dashboard',
    'Multi-site management capabilities',
    'Mobile and web-based access',
    'Integration with existing systems',
    'Custom alert and notification system'
  ];

  const benefits = [
    { title: 'Cost Reduction', description: '8-15% reduction in energy costs through intelligent optimization', icon: TrendingUp },
    { title: 'Quick ROI', description: 'Payback period of just 1.5-2 years with immediate savings', icon: Award },
    { title: 'Sustainability', description: 'Reduce carbon footprint and meet environmental goals', icon: Leaf },
    { title: 'Compliance', description: 'Meet international energy management standards', icon: Shield }
  ];

  return (
    <div className="font-['Open_Sans'] min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 w-full overflow-x-hidden">
      <PageLayout
        title="On-Premise Systems"
        subtitle="Smart Energy Management System (EnMS)"
        category="conserve"
      >
        {/* Simple Hero Section */}
        <div className="relative overflow-hidden bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
          <div className="px-4 sm:px-6 lg:px-8 py-8 sm:py-10 lg:py-12">
            <div className="max-w-4xl mx-auto text-center">
              <div className="space-y-4">
                {/* Simple Badge */}
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 border border-green-200">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-sm font-semibold text-green-700 font-['Open_Sans']">Next-Gen Energy Solutions</span>
                </div>

                {/* Main Heading */}
                <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black leading-tight font-['Open_Sans']">
                  <span className="text-gray-900">Smart </span>
                  <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                    On-Premise
                  </span>
                  <span className="text-gray-900"> Energy Management</span>
                </h1>

                {/* Description */}
                <p className="text-lg sm:text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto font-['Open_Sans']">
                  Transform your energy infrastructure with our comprehensive EnMS platform designed for
                  <span className="text-green-700 font-semibold"> maximum efficiency</span>,
                  <span className="text-emerald-700 font-semibold"> real-time insights</span>, and
                  <span className="text-teal-700 font-semibold"> sustainable operations</span>
                </p>

                {/* AI Badge */}
                <div className="inline-flex items-center px-6 py-3 rounded-full bg-green-600 text-white shadow-lg">
                  <Sparkles className="w-5 h-5 mr-2" />
                  <span className="font-semibold font-['Open_Sans']">AI-Powered Dashboard</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section - Table Format */}
        <div ref={statsRef} className="py-8 sm:py-10 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-6 sm:mb-8">
              <h2 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Proven Performance
                </span>
              </h2>
              <p className="text-base sm:text-lg text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']">
                Savings right out of the box
              </p>
            </div>

            {/* Table-style Stats Display */}
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-2xl border-2 border-green-100 overflow-hidden max-w-4xl mx-auto">
              {/* Table Header Row */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 bg-gradient-to-r from-green-50 to-emerald-50 border-b-2 border-green-100">
                {stats.map((stat, index) => (
                  <div key={index} className="p-4 sm:p-6 text-center border-b sm:border-b-0 sm:border-r border-green-100 last:border-b-0 last:border-r-0">
                    <h3 className="text-sm sm:text-base lg:text-lg font-black text-gray-900 leading-tight font-['Open_Sans']">
                      {stat.label}
                    </h3>
                  </div>
                ))}
              </div>

              {/* Table Values Row */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 bg-white">
                {stats.map((stat, index) => {
                  const getDisplayValue = () => {
                    if (stat.countTo === 300) {
                      return countsStarted ? `${customersCount}+` : '0+';
                    } else if (stat.countTo === 20000) {
                      return countsStarted ? `${devicesCount.toLocaleString()}+` : '0+';
                    }
                    return stat.displayValue;
                  };

                  return (
                    <div key={index} className="p-6 sm:p-8 text-center border-b sm:border-b-0 sm:border-r border-green-100 last:border-b-0 last:border-r-0 group hover:bg-green-50 transition-all duration-300">
                      <div className="text-2xl sm:text-3xl lg:text-4xl font-black text-green-700 group-hover:text-green-800 transition-colors font-['Open_Sans']">
                        {getDisplayValue()}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Product Overview Tabs */}
        <div className="py-8 sm:py-10 lg:py-12 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-8 sm:mb-10">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-3 sm:mb-4 font-['Open_Sans']">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Comprehensive Energy
                </span>
                <br />
                <span className="text-gray-800">Management Solution</span>
              </h2>
              <p className="text-base sm:text-lg lg:text-xl xl:text-2xl text-gray-700 max-w-4xl mx-auto font-medium leading-relaxed font-['Open_Sans']">
                Transform how you monitor, manage, and optimize energy resources across your entire operations with our
                <span className="text-green-600 font-bold"> AI-powered platform</span>
              </p>
            </div>

            {/* Tab Navigation */}
            <div className="flex flex-wrap justify-center mb-8 sm:mb-10 bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-2 sm:p-3 shadow-2xl max-w-4xl mx-auto border border-green-100/50">
              {[
                { id: 'overview', label: 'Overview', icon: Target },
                { id: 'features', label: 'Core Features', icon: Settings },
                { id: 'benefits', label: 'Benefits', icon: Award },
                { id: 'modules', label: 'Add-on Modules', icon: Globe }
              ].map((tab) => {
                const TabIcon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`group flex items-center px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4 rounded-lg sm:rounded-xl font-bold text-sm sm:text-base lg:text-lg transition-all duration-300 font-['Open_Sans'] m-1 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-green-600 to-emerald-600 text-white shadow-xl transform scale-105'
                        : 'text-gray-700 hover:text-green-600 hover:bg-green-50/80 hover:scale-102'
                    }`}
                  >
                    <TabIcon className={`w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 transition-all ${
                      activeTab === tab.id ? 'text-green-200' : 'text-gray-500 group-hover:text-green-500'
                    }`} />
                    <span className="hidden sm:inline">{tab.label}</span>
                    <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
                  </button>
                );
              })}
            </div>

            {/* Tab Content */}
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 lg:p-8 xl:p-10 border border-green-100/50">
              {activeTab === 'overview' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-10 items-center">
                  <div className="space-y-4 sm:space-y-6">
                    <div className="space-y-3">
                      <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 font-['Open_Sans']">
                        Why Choose
                        <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent"> On-Premise EnMS?</span>
                      </h3>
                      <p className="text-base sm:text-lg text-gray-800 leading-relaxed font-bold font-['Open_Sans']">
                        In today's competitive industrial landscape, optimizing energy consumption isn't just about reducing costs—it's about
                        <span className="text-green-700 font-black"> sustainability, compliance, and gaining a competitive edge</span>.
                        Our On-Premise EnMS delivers a comprehensive solution that transforms how you monitor, manage, and optimize resources across your entire operations.
                      </p>
                    </div>

                    <div className="space-y-3 sm:space-y-4">
                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 hover:shadow-lg transition-all">
                        <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Cost Optimization</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">Reduce energy costs by 8-15% through intelligent monitoring and optimization.</p>
                        </div>
                      </div>

                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-100 hover:shadow-lg transition-all">
                        <div className="bg-emerald-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Sustainability Goals</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">Meet environmental targets with comprehensive carbon footprint management.</p>
                        </div>
                      </div>

                      <div className="group flex items-start space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-teal-50 to-green-50 rounded-xl border border-teal-100 hover:shadow-lg transition-all">
                        <div className="bg-teal-600 p-2 rounded-lg group-hover:scale-110 transition-transform flex-shrink-0">
                          <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-black text-gray-900 text-base sm:text-lg mb-1 font-['Open_Sans']">Compliance Ready</h4>
                          <p className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">Built-in support for ISO 50001 and international energy standards.</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="relative mt-6 lg:mt-0">
                    <div className="relative group">
                      <img
                        src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=700&h=500&fit=crop&auto=format"
                        alt="Energy Management Overview"
                        className="w-full h-auto rounded-xl sm:rounded-2xl shadow-2xl group-hover:scale-105 transition-all duration-500 border-2 sm:border-4 border-green-100"
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-green-600/30 via-transparent to-transparent rounded-xl sm:rounded-2xl"></div>
                    </div>

                    {/* Floating Elements */}
                    <div className="absolute -top-2 sm:-top-4 -right-2 sm:-right-4 bg-green-600 text-white p-2 sm:p-3 rounded-lg sm:rounded-xl shadow-xl">
                      <div className="flex items-center space-x-1 sm:space-x-2">
                        <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                        <span className="font-bold text-xs sm:text-sm font-['Open_Sans']">AI-Powered</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'features' && (
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Core Platform Features
                      </span>
                    </h3>
                    <p className="text-base sm:text-lg lg:text-xl text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']">
                      Powerful capabilities designed to transform your energy management
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    {coreFeatures.map((feature, index) => (
                      <div key={index} className="group">
                        <div className="flex items-start space-x-3 sm:space-x-4 p-4 sm:p-6 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-xl sm:rounded-2xl border border-green-100 hover:border-green-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                          <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform shadow-lg flex-shrink-0">
                            <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                          </div>
                          <span className="text-gray-800 font-bold text-base sm:text-lg leading-relaxed font-['Open_Sans']">{feature}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'benefits' && (
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Key Benefits
                      </span>
                    </h3>
                    <p className="text-base sm:text-lg lg:text-xl text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']">
                      Discover the transformative advantages of our energy management solution
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    {benefits.map((benefit, index) => {
                      const IconComponent = benefit.icon;
                      const gradients = [
                        'from-green-500 to-emerald-600',
                        'from-emerald-500 to-teal-600',
                        'from-teal-500 to-green-600',
                        'from-green-600 to-emerald-700'
                      ];
                      return (
                        <div key={index} className="group">
                          <div className="relative p-6 sm:p-8 bg-white rounded-xl sm:rounded-2xl border border-green-100 hover:border-green-300 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
                            {/* Background Gradient */}
                            <div className={`absolute top-0 left-0 w-full h-2 bg-gradient-to-r ${gradients[index % gradients.length]}`}></div>

                            <div className="flex items-start space-x-4 sm:space-x-6">
                              <div className={`bg-gradient-to-br ${gradients[index % gradients.length]} p-3 sm:p-4 rounded-xl sm:rounded-2xl shadow-lg group-hover:scale-110 transition-transform flex-shrink-0`}>
                                <IconComponent className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-lg sm:text-xl lg:text-2xl font-black text-gray-900 mb-2 group-hover:text-green-700 transition-colors font-['Open_Sans']">{benefit.title}</h4>
                                <p className="text-gray-800 font-bold text-base sm:text-lg leading-relaxed font-['Open_Sans']">{benefit.description}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {activeTab === 'modules' && (
                <div className="space-y-6 sm:space-y-8">
                  <div className="text-center">
                    <h3 className="text-2xl sm:text-3xl lg:text-4xl font-black text-gray-900 mb-3 font-['Open_Sans']">
                      <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        Specialized Add-on Modules
                      </span>
                    </h3>
                    <p className="text-base sm:text-lg lg:text-xl text-gray-800 font-bold max-w-2xl mx-auto font-['Open_Sans']">
                      Extend your energy management capabilities with powerful specialized modules
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                    {addOnModules.map((module, index) => {
                      const IconComponent = module.icon;
                      return (
                        <div key={index} className="group">
                          <div
                            className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-xl sm:rounded-2xl p-6 sm:p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 cursor-pointer transform hover:-translate-y-2"
                            onClick={() => setSelectedModule(selectedModule === index ? null : index)}
                          >
                            <div className="flex items-center space-x-3 sm:space-x-4 mb-3 sm:mb-4">
                              <div className="bg-gradient-to-br from-green-500 to-emerald-600 group-hover:from-emerald-500 group-hover:to-teal-600 p-2 sm:p-3 rounded-lg sm:rounded-xl transition-all duration-300 shadow-lg flex-shrink-0">
                                <IconComponent className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                              </div>
                              <h4 className="font-black text-gray-900 text-base sm:text-lg group-hover:text-green-700 transition-colors font-['Open_Sans']">{module.title}</h4>
                            </div>

                            <p className="text-gray-800 font-bold mb-3 sm:mb-4 leading-relaxed text-sm sm:text-base font-['Open_Sans']">{module.description}</p>

                            {selectedModule === index && (
                              <div className="space-y-2 pt-3 sm:pt-4 border-t border-green-100 animate-in slide-in-from-top duration-300">
                                {module.features.map((feature, featureIndex) => (
                                  <div key={featureIndex} className="flex items-start space-x-2 sm:space-x-3">
                                    <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-700 flex-shrink-0 mt-0.5" />
                                    <span className="text-gray-800 font-bold text-sm sm:text-base font-['Open_Sans']">{feature}</span>
                                  </div>
                                ))}
                              </div>
                            )}

                            <div className="flex items-center text-green-700 font-black mt-3 sm:mt-4 group-hover:text-emerald-700 transition-colors font-['Open_Sans']">
                              <span className="text-sm sm:text-base">{selectedModule === index ? 'Hide Details' : 'View Details'}</span>
                              <ArrowRight className={`w-4 h-4 sm:w-5 sm:h-5 ml-2 transition-transform duration-300 ${selectedModule === index ? 'rotate-90' : 'group-hover:translate-x-1'}`} />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Contact Section - Separate & Distinct */}
        <div className="mt-8 sm:mt-12 py-8 sm:py-12 bg-gradient-to-br from-green-100 via-green-50 to-emerald-50 border-t-4 border-green-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Main Heading */}
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-black text-green-700 mb-4 sm:mb-6 font-['Open_Sans']">
              Need a Custom Energy Management Solution?
            </h2>

            {/* Description */}
            <p className="text-base sm:text-lg lg:text-xl text-green-600 font-bold max-w-3xl mx-auto leading-relaxed mb-6 sm:mb-8 font-['Open_Sans']">
              Our engineers can design energy management systems to your specific requirements with industry-leading
              performance and reliability. Get in touch today to discuss your needs.
            </p>

            {/* Contact Support Button */}
            <button
              onClick={() => navigate('/contact/sales')}
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold text-lg rounded-full shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 font-['Open_Sans']"
            >
              <Users className="w-6 h-6 mr-3" />
              Contact Support
            </button>
          </div>
        </div>

        {/* Custom Animations */}
        <style>{`
          @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(3deg); }
          }
          @keyframes float-delayed {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-12px) rotate(-2deg); }
          }
          .animate-float {
            animation: float 5s ease-in-out infinite;
          }
          .animate-float-delayed {
            animation: float-delayed 6s ease-in-out infinite;
          }
        `}</style>
      </PageLayout>
    </div>
  );
};

export default OnPremiseSystemsPage;
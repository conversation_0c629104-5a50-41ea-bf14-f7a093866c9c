import React, { useState } from 'react';
import {
  Building2,
  Zap,
  Users,
  BarChart3,
  Shield,
  Smartphone,
  Clock,
  DollarSign,
  CheckCircle,
  ArrowRight,
  Home,
  Wrench,
  Building,
  TrendingUp,
  Bell,
  CreditCard,
  Gauge,
  Bot,
  Eye,
  Briefcase,
  Download,
  Calendar,
  Award,
  Monitor,
  Lightbulb
} from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

const LightingEnergySaverPage = () => {
  const [activeFeature, setActiveFeature] = useState(0);

  const stats = [
    { label: 'Energy Savings', value: '30-50%', icon: TrendingUp, color: 'from-green-500 to-emerald-600' },
    { label: 'ROI Period', value: '1-2 Years', icon: Award, color: 'from-emerald-500 to-teal-600' },
    { label: 'Smart Controls', value: '24/7', icon: Monitor, color: 'from-teal-500 to-green-600' },
    { label: 'LED Lifespan', value: '50,000h', icon: Lightbulb, color: 'from-green-600 to-emerald-700' }
  ];

  const features = [
    {
      title: "Smart LED Technology",
      description: "Advanced LED lighting systems with intelligent controls for optimal energy efficiency and superior illumination quality."
    },
    {
      title: "Automated Controls",
      description: "Motion sensors, daylight harvesting, and programmable scheduling for maximum energy savings and convenience."
    },
    {
      title: "Energy Monitoring",
      description: "Real-time energy consumption tracking with detailed analytics and optimization recommendations."
    },
    {
      title: "Remote Management",
      description: "Cloud-based control system for monitoring and managing lighting across multiple locations from anywhere."
    },
    {
      title: "Maintenance Alerts",
      description: "Predictive maintenance notifications and automated fault detection to minimize downtime and costs."
    },
    {
      title: "Custom Solutions",
      description: "Tailored lighting designs for specific environments including offices, warehouses, retail, and industrial spaces."
    }
  ];

  const benefits = [
    { title: "Energy Savings", desc: "Reduce lighting energy consumption by 30-50%" },
    { title: "Lower Maintenance", desc: "LED lifespan up to 50,000 hours reduces replacement costs" },
    { title: "Improved Productivity", desc: "Better lighting quality enhances workplace performance" },
    { title: "Environmental Impact", desc: "Significant reduction in carbon footprint" },
    { title: "Smart Controls", desc: "Automated systems optimize usage patterns" }
  ];

  const industries = [
    { title: "Office Buildings" },
    { title: "Manufacturing Facilities" },
    { title: "Retail Stores" },
    { title: "Warehouses" },
    { title: "Educational Institutions" },
    { title: "Healthcare Facilities" }
  ];

  return (
    <div className="font-['Open_Sans'] min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <PageLayout
        title="Lighting Energy Saver"
        subtitle="Smart LED Lighting Solutions"
        category="conserve"
      >
        {/* Modern Blended Hero Section */}
        <div className="relative overflow-hidden">
          {/* Seamless Background Blend */}
          <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50"></div>

          {/* Organic Floating Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-green-200/30 to-emerald-300/20 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 bg-gradient-to-br from-emerald-200/25 to-teal-300/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-br from-teal-200/20 to-green-300/15 rounded-full blur-xl animate-pulse delay-500"></div>
          </div>

          {/* Subtle Grid Pattern */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23059669' stroke-width='1'%3E%3Cpath d='M0 0h100v100H0z'/%3E%3Cpath d='M0 50h100M50 0v100'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-12 gap-12 lg:gap-16 items-center">
                {/* Left Content - 7 columns */}
                <div className="lg:col-span-7 text-center lg:text-left space-y-8">
                  {/* Floating Badge */}
                  <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/80 backdrop-blur-sm border border-green-200/50 shadow-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></div>
                    <span className="text-sm font-bold text-green-700 tracking-wide">Smart LED Technology</span>
                  </div>

                  {/* Main Heading */}
                  <div className="space-y-4">
                    <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black leading-tight">
                      <span className="text-gray-900 block">Smart</span>
                      <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent block">
                        Lighting Energy
                      </span>
                      <span className="text-gray-800 block">Solutions</span>
                    </h1>

                    {/* Decorative Line */}
                    <div className="flex justify-center lg:justify-start">
                      <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-lg lg:text-xl text-gray-800 leading-relaxed font-semibold max-w-2xl mx-auto lg:mx-0">
                    Transform your lighting infrastructure with our advanced LED solutions designed for
                    <span className="text-green-700 font-black"> maximum energy savings</span>,
                    <span className="text-emerald-700 font-black"> intelligent automation</span>, and
                    <span className="text-teal-700 font-black"> superior performance</span>
                  </p>

                  {/* Key Highlights */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-green-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-green-700" />
                      <span className="text-sm font-bold text-gray-800">30-50% Energy Savings</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-emerald-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-emerald-700" />
                      <span className="text-sm font-bold text-gray-800">50,000h LED Life</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-teal-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-teal-700" />
                      <span className="text-sm font-bold text-gray-800">Smart Controls</span>
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-4">
                    <button className="group bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                      <Download className="w-5 h-5 mr-3 group-hover:animate-bounce" />
                      Get Lighting Guide
                    </button>
                    <button className="group bg-white/80 backdrop-blur-sm text-green-700 px-8 py-4 rounded-2xl font-bold text-lg border-2 border-green-200 hover:border-green-400 hover:bg-white transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                      <Calendar className="w-5 h-5 mr-3 group-hover:animate-pulse" />
                      Schedule Assessment
                    </button>
                  </div>
                </div>

                {/* Right Content - 5 columns */}
                <div className="lg:col-span-5 relative">
                  <div className="relative group">
                    {/* Main Image */}
                    <div className="relative overflow-hidden rounded-3xl shadow-2xl">
                      <img
                        src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop&auto=format"
                        alt="Smart LED Lighting System"
                        className="w-full h-auto object-cover group-hover:scale-105 transition-all duration-700"
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-green-900/20 via-transparent to-transparent"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="py-16 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-black text-gray-900 mb-4">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Lighting Performance
                </span>
              </h2>
              <p className="text-lg text-gray-800 font-bold max-w-2xl mx-auto">
                Experience exceptional results with our smart LED lighting solutions
              </p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="group">
                    <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100 hover:border-green-300 transform hover:-translate-y-1">
                      <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br ${stat.color} mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div className="text-2xl lg:text-3xl font-black text-gray-900 mb-2 group-hover:text-green-700 transition-colors">
                        {stat.value}
                      </div>
                      <div className="text-gray-800 font-black text-sm">{stat.label}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Industries Section */}
        <div className="py-20 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Perfect for Every
                </span>
                <br />
                <span className="text-gray-800">Industry</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-800 max-w-4xl mx-auto font-bold leading-relaxed">
                Our smart LED lighting solutions adapt to various environments and
                <span className="text-green-700 font-black"> business needs</span>
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {industries.map((industry, index) => (
                <div key={index} className="group">
                  <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 transform hover:-translate-y-2">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      {index === 0 && <Building className="w-8 h-8 text-white" />}
                      {index === 1 && <Wrench className="w-8 h-8 text-white" />}
                      {index === 2 && <Building2 className="w-8 h-8 text-white" />}
                      {index === 3 && <Home className="w-8 h-8 text-white" />}
                      {index === 4 && <Users className="w-8 h-8 text-white" />}
                      {index === 5 && <Shield className="w-8 h-8 text-white" />}
                    </div>
                    <h3 className="text-xl font-black text-gray-900 mb-4 group-hover:text-green-700 transition-colors">
                      {industry.title}
                    </h3>
                    <div className="h-1 w-0 bg-gradient-to-r from-green-500 to-emerald-500 group-hover:w-full transition-all duration-500 rounded-full"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-20 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Smart Features
                </span>
                <br />
                <span className="text-gray-800">& Capabilities</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-800 max-w-4xl mx-auto font-bold leading-relaxed">
                Advanced lighting technology with intelligent controls for
                <span className="text-green-700 font-black"> maximum efficiency</span>
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="group">
                  <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 transform hover:-translate-y-2">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      {index === 0 && <Lightbulb className="w-8 h-8 text-white" />}
                      {index === 1 && <Gauge className="w-8 h-8 text-white" />}
                      {index === 2 && <BarChart3 className="w-8 h-8 text-white" />}
                      {index === 3 && <Monitor className="w-8 h-8 text-white" />}
                      {index === 4 && <Bell className="w-8 h-8 text-white" />}
                      {index === 5 && <Eye className="w-8 h-8 text-white" />}
                    </div>
                    <h3 className="text-xl font-black text-gray-900 mb-4 group-hover:text-green-700 transition-colors">
                      {feature.title}
                    </h3>
                    <p className="text-gray-800 leading-relaxed font-bold">
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="py-20 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  LED Lighting Benefits
                </span>
                <br />
                <span className="text-gray-800">for Your Business</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-800 max-w-4xl mx-auto font-bold leading-relaxed">
                Experience transformative advantages with our smart LED lighting solutions
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="group">
                  <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 transform hover:-translate-y-2">
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                      {index === 0 && <TrendingUp className="w-8 h-8 text-white" />}
                      {index === 1 && <Clock className="w-8 h-8 text-white" />}
                      {index === 2 && <Users className="w-8 h-8 text-white" />}
                      {index === 3 && <Shield className="w-8 h-8 text-white" />}
                      {index === 4 && <BarChart3 className="w-8 h-8 text-white" />}
                    </div>
                    <h3 className="text-xl font-black text-gray-900 mb-4 group-hover:text-green-700 transition-colors">
                      {benefit.title}
                    </h3>
                    <p className="text-gray-800 leading-relaxed font-bold">
                      {benefit.desc}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Compact CTA Section */}
        <div className="py-12 bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 relative overflow-hidden">
          {/* Subtle Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          {/* Floating Elements */}
          <div className="absolute top-4 left-10 w-16 h-16 bg-white/10 rounded-full blur-lg animate-pulse"></div>
          <div className="absolute bottom-4 right-10 w-20 h-20 bg-emerald-300/10 rounded-full blur-xl animate-pulse delay-700"></div>

          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-2xl">
              <div className="grid lg:grid-cols-2 gap-8 items-center">
                {/* Left Content */}
                <div className="text-center lg:text-left">
                  <h2 className="text-2xl lg:text-3xl font-black text-white mb-3">
                    Ready to <span className="text-green-200">Illuminate</span> Your Energy Savings?
                  </h2>
                  <p className="text-green-100 font-bold mb-6 text-lg">
                    Join <span className="text-white font-black">1000+</span> businesses achieving
                    <span className="text-green-200 font-black"> 30-50% energy savings</span>
                  </p>

                  {/* Quick Stats */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start mb-6">
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">30-50%</div>
                      <div className="text-green-200 text-xs">Energy Savings</div>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">50,000h</div>
                      <div className="text-green-200 text-xs">LED Life</div>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">Smart</div>
                      <div className="text-green-200 text-xs">Controls</div>
                    </div>
                  </div>
                </div>

                {/* Right Content - Action Buttons */}
                <div className="flex flex-col sm:flex-row lg:flex-col gap-4">
                  <button className="group bg-white text-green-700 px-6 py-3 rounded-xl font-bold text-lg hover:bg-green-50 transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                    <Download className="w-5 h-5 mr-3 group-hover:animate-pulse" />
                    Get LED Assessment
                  </button>
                  <button className="group border-2 border-white text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-white hover:text-green-700 transition-all duration-300 flex items-center justify-center backdrop-blur-sm transform hover:-translate-y-1">
                    <Calendar className="w-5 h-5 mr-3 group-hover:animate-bounce" />
                    Schedule Consultation
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    </div>
  );
};

export default LightingEnergySaverPage;
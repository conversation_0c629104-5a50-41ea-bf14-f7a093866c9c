import React, { useState } from 'react';
import { ChevronDown, Brain, Shield, Zap, Target, Cpu, Globe, BarChart3, Settings, CheckCircle, ArrowRight, Download, Calendar, TrendingUp, Award, Users, Monitor } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const CloudEnergyManagementPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const stats = [
    { label: 'Cloud Efficiency', value: '99.9%', icon: TrendingUp, color: 'from-green-500 to-emerald-600' },
    { label: 'Cost Reduction', value: '25-40%', icon: Award, color: 'from-emerald-500 to-teal-600' },
    { label: 'Global Users', value: '500+', icon: Users, color: 'from-teal-500 to-green-600' },
    { label: 'Data Points', value: '50M+', icon: Monitor, color: 'from-green-600 to-emerald-700' }
  ];

  const features: Feature[] = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: "Intelligent Closed-Loop Operations",
      description: "Drive new generation systems powered by real-time digital data and intelligent orchestration for streamlined workflows."
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "Predictive Edge-Based AIoT Platform",
      description: "Leverage intelligent AIoT design to analyze data, deliver predictive insights and enable early intervention."
    },
    {
      icon: <Target className="w-8 h-8" />,
      title: "Depth of Traceability & Genealogy",
      description: "Embedded Digital Twin technology offers high degree of spatial awareness across workflows with precise tracking."
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Distributed Location Production",
      description: "Drive production orchestration across multiple sites with unified view of operations globally."
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Automatic Anomaly Detection",
      description: "Catch production anomalies before they impact delivery schedules with intelligent alerts and contextual analysis."
    },
    {
      icon: <Settings className="w-8 h-8" />,
      title: "Digital Factory Synchronization",
      description: "Optimize digital workflows, reduce wait times and eliminate unnecessary delays automatically."
    }
  ];

  const benefits: string[] = [
    "Deliver productivity with optimal resources",
    "Enable quality in process and by design",
    "Balance cost with adaptability to changes",
    "Elevate engineering for accelerated innovation"
  ];

  return (
    <div className="font-['Open_Sans'] min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <PageLayout
        title="Cloud Energy Management"
        subtitle="Next Generation Cloud-Based Energy Solutions"
        category="conserve"
      >
        {/* Modern Blended Hero Section */}
        <div className="relative overflow-hidden">
          {/* Seamless Background Blend */}
          <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50"></div>

          {/* Organic Floating Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-green-200/30 to-emerald-300/20 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 bg-gradient-to-br from-emerald-200/25 to-teal-300/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-br from-teal-200/20 to-green-300/15 rounded-full blur-xl animate-pulse delay-500"></div>
          </div>

          {/* Subtle Grid Pattern */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23059669' stroke-width='1'%3E%3Cpath d='M0 0h100v100H0z'/%3E%3Cpath d='M0 50h100M50 0v100'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-12 gap-12 lg:gap-16 items-center">
                {/* Left Content - 7 columns */}
                <div className="lg:col-span-7 text-center lg:text-left space-y-8">
                  {/* Floating Badge */}
                  <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/80 backdrop-blur-sm border border-green-200/50 shadow-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></div>
                    <span className="text-sm font-bold text-green-700 tracking-wide">Cloud-Powered Energy Intelligence</span>
                  </div>

                  {/* Main Heading */}
                  <div className="space-y-4">
                    <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black leading-tight">
                      <span className="text-gray-900 block">Smart</span>
                      <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent block">
                        Cloud Energy
                      </span>
                      <span className="text-gray-800 block">Management</span>
                    </h1>

                    {/* Decorative Line */}
                    <div className="flex justify-center lg:justify-start">
                      <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-lg lg:text-xl text-gray-800 leading-relaxed font-semibold max-w-2xl mx-auto lg:mx-0">
                    Transform your energy operations with our advanced cloud-based platform designed for
                    <span className="text-green-700 font-black"> scalable efficiency</span>,
                    <span className="text-emerald-700 font-black"> global accessibility</span>, and
                    <span className="text-teal-700 font-black"> intelligent automation</span>
                  </p>

                  {/* Key Highlights */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-green-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-green-700" />
                      <span className="text-sm font-bold text-gray-800">99.9% Uptime</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-emerald-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-emerald-700" />
                      <span className="text-sm font-bold text-gray-800">Global Access</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-teal-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-teal-700" />
                      <span className="text-sm font-bold text-gray-800">AI-Powered</span>
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-4">
                    <button className="group bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                      <Download className="w-5 h-5 mr-3 group-hover:animate-bounce" />
                      Get Cloud Guide
                    </button>
                    <button className="group bg-white/80 backdrop-blur-sm text-green-700 px-8 py-4 rounded-2xl font-bold text-lg border-2 border-green-200 hover:border-green-400 hover:bg-white transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                      <Calendar className="w-5 h-5 mr-3 group-hover:animate-pulse" />
                      Schedule Demo
                    </button>
                  </div>
                </div>

                {/* Right Content - 5 columns */}
                <div className="lg:col-span-5 relative">
                  <div className="relative group">
                    {/* Main Image */}
                    <div className="relative overflow-hidden rounded-3xl shadow-2xl">
                      <img
                        src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&h=600&fit=crop&auto=format"
                        alt="Cloud Energy Management System"
                        className="w-full h-auto object-cover group-hover:scale-105 transition-all duration-700"
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-green-900/20 via-transparent to-transparent"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="py-16 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-black text-gray-900 mb-4">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Cloud Performance
                </span>
              </h2>
              <p className="text-lg text-gray-800 font-bold max-w-2xl mx-auto">
                Experience unmatched reliability and efficiency with our cloud-based energy management platform
              </p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="group">
                    <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100 hover:border-green-300 transform hover:-translate-y-1">
                      <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br ${stat.color} mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div className="text-2xl lg:text-3xl font-black text-gray-900 mb-2 group-hover:text-green-700 transition-colors">
                        {stat.value}
                      </div>
                      <div className="text-gray-800 font-black text-sm">{stat.label}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-20 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Why Choose Cloud Energy
                </span>
                <br />
                <span className="text-gray-800">Management?</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-800 max-w-4xl mx-auto font-bold leading-relaxed">
                Experience the power of cloud-based energy management with advanced AI capabilities and
                <span className="text-green-700 font-black"> global accessibility</span>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="group bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 transform hover:-translate-y-2"
                >
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-black text-gray-900 mb-4 group-hover:text-green-700 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-800 leading-relaxed font-bold">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="py-20 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Cloud Energy Benefits
                </span>
                <br />
                <span className="text-gray-800">for Your Business</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-800 max-w-4xl mx-auto font-bold leading-relaxed">
                Experience transformative advantages with our cloud-based energy management platform
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="group">
                    <div className="flex items-start space-x-4 p-6 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-2xl border border-green-100 hover:border-green-300 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                      <div className="flex-shrink-0">
                        <div className="bg-green-600 p-2 rounded-lg group-hover:scale-110 transition-transform shadow-lg">
                          <CheckCircle className="w-6 h-6 text-white" />
                        </div>
                      </div>
                      <p className="text-lg text-gray-800 font-bold leading-relaxed group-hover:text-green-700 transition-colors">
                        {benefit}
                      </p>
                    </div>
                  </div>
                ))}

                <div className="mt-8">
                  <button className="group bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 px-8 py-4 rounded-2xl font-bold text-lg text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 flex items-center space-x-2">
                    <span>Learn More</span>
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>

              <div className="relative">
                <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-300">
                  <div className="bg-gradient-to-br from-green-500 to-emerald-600 p-4 rounded-2xl w-20 h-20 flex items-center justify-center mb-6 shadow-lg">
                    <BarChart3 className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-black text-gray-900 mb-4">
                    Cloud Performance Enhancement
                  </h3>
                  <p className="text-gray-800 font-bold leading-relaxed">
                    Achieve optimal energy efficiency through cloud-based analytics, real-time monitoring,
                    and intelligent automation that scales with your business needs across multiple locations.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Cloud Intelligence Section */}
        <div className="py-20 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Cloud Intelligence
                </span>
                <br />
                <span className="text-gray-800">& Advanced Analytics</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-800 max-w-4xl mx-auto font-bold leading-relaxed">
                Harness the power of cloud computing for real-time energy insights, predictive analytics,
                and intelligent automation across your entire energy infrastructure.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="group">
                <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 text-center hover:shadow-2xl hover:border-green-300 transition-all duration-500 transform hover:-translate-y-2">
                  <div className="bg-gradient-to-br from-green-500 to-emerald-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Cpu className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-black text-gray-900 mb-4 group-hover:text-green-700 transition-colors">Predictive Modeling</h3>
                  <p className="text-gray-800 font-bold leading-relaxed">Advanced AI algorithms simulate energy scenarios and predict optimal consumption patterns</p>
                </div>
              </div>

              <div className="group">
                <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 text-center hover:shadow-2xl hover:border-green-300 transition-all duration-500 transform hover:-translate-y-2">
                  <div className="bg-gradient-to-br from-emerald-500 to-teal-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Settings className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-black text-gray-900 mb-4 group-hover:text-emerald-700 transition-colors">Smart Optimization</h3>
                  <p className="text-gray-800 font-bold leading-relaxed">Automated performance tuning and real-time adjustments for maximum efficiency</p>
                </div>
              </div>

              <div className="group">
                <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 text-center hover:shadow-2xl hover:border-green-300 transition-all duration-500 transform hover:-translate-y-2">
                  <div className="bg-gradient-to-br from-teal-500 to-green-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <BarChart3 className="w-10 h-10 text-white" />
                  </div>
                  <h3 className="text-xl font-black text-gray-900 mb-4 group-hover:text-teal-700 transition-colors">Proactive Insights</h3>
                  <p className="text-gray-800 font-bold leading-relaxed">Intelligent alerts and actionable recommendations for continuous improvement</p>
                </div>
              </div>
            </div>
          </div>
        </div>



        {/* Compact CTA Section */}
        <div className="py-12 bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 relative overflow-hidden">
          {/* Subtle Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          {/* Floating Elements */}
          <div className="absolute top-4 left-10 w-16 h-16 bg-white/10 rounded-full blur-lg animate-pulse"></div>
          <div className="absolute bottom-4 right-10 w-20 h-20 bg-emerald-300/10 rounded-full blur-xl animate-pulse delay-700"></div>

          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-2xl">
              <div className="grid lg:grid-cols-2 gap-8 items-center">
                {/* Left Content */}
                <div className="text-center lg:text-left">
                  <h2 className="text-2xl lg:text-3xl font-black text-white mb-3">
                    Ready to <span className="text-green-200">Scale</span> Your Energy Management?
                  </h2>
                  <p className="text-green-100 font-bold mb-6 text-lg">
                    Join <span className="text-white font-black">500+</span> organizations leveraging cloud-based
                    <span className="text-green-200 font-black"> energy intelligence</span>
                  </p>

                  {/* Quick Stats */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start mb-6">
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">99.9%</div>
                      <div className="text-green-200 text-xs">Uptime</div>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">Global</div>
                      <div className="text-green-200 text-xs">Access</div>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">AI-Powered</div>
                      <div className="text-green-200 text-xs">Analytics</div>
                    </div>
                  </div>
                </div>

                {/* Right Content - Action Buttons */}
                <div className="flex flex-col sm:flex-row lg:flex-col gap-4">
                  <button className="group bg-white text-green-700 px-6 py-3 rounded-xl font-bold text-lg hover:bg-green-50 transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                    <Download className="w-5 h-5 mr-3 group-hover:animate-pulse" />
                    Start Cloud Trial
                  </button>
                  <button className="group border-2 border-white text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-white hover:text-green-700 transition-all duration-300 flex items-center justify-center backdrop-blur-sm transform hover:-translate-y-1">
                    <Calendar className="w-5 h-5 mr-3 group-hover:animate-bounce" />
                    Schedule Demo
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    </div>
  );
};

export default CloudEnergyManagementPage;
import React, { useState } from 'react';
import { CheckCircle, Shield, Zap, Database, Eye, TrendingUp, ArrowRight, PlayCircle, Download, Calendar, Award, Monitor, BarChart3, Users, Building } from 'lucide-react';
import PageLayout from "@/components/layout/PageLayout";

const EnergyAuditsPage = () => {
  const [hoveredBenefit, setHoveredBenefit] = useState<number | null>(null);

  const stats = [
    { label: 'Energy Savings', value: '15-30%', icon: TrendingUp, color: 'from-green-500 to-emerald-600' },
    { label: 'Audit Accuracy', value: '99.9%', icon: Award, color: 'from-emerald-500 to-teal-600' },
    { label: 'Compliance Rate', value: '100%', icon: Shield, color: 'from-teal-500 to-green-600' },
    { label: 'Standards Supported', value: '50+', icon: Monitor, color: 'from-green-600 to-emerald-700' }
  ];

  const benefits = [
    {
      icon: <Database className="w-6 h-6" />,
      title: "Seamless Data Collection",
      description: "Automatic aggregation from ERP, Finance, e-Procurement, Supply Chain, and IoT devices"
    },
    {
      icon: <Eye className="w-6 h-6" />,
      title: "AI-Powered Anomaly Detection",
      description: "Real-time error detection and flagging before report finalization"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Regulatory Compliance",
      description: "Pre-built templates for BRSR, GRI, and CSRD standards"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Faster Reporting",
      description: "Drastically reduce reporting time and consultant dependency"
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: "100% Data Trail",
      description: "Complete audit-friendly verification and traceability"
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Real-time Insights",
      description: "Live dashboards and alerts for quick improvement identification"
    }
  ];

  return (
    <div className="font-['Open_Sans'] min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
      <PageLayout
        title="Energy Audits"
        subtitle="Enterprise ESG Reporting Platform"
        category="conserve"
      >
        {/* Modern Blended Hero Section */}
        <div className="relative overflow-hidden">
          {/* Seamless Background Blend */}
          <div className="absolute inset-0 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50"></div>

          {/* Organic Floating Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-green-200/30 to-emerald-300/20 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-80 h-80 bg-gradient-to-br from-emerald-200/25 to-teal-300/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-br from-teal-200/20 to-green-300/15 rounded-full blur-xl animate-pulse delay-500"></div>
          </div>

          {/* Subtle Grid Pattern */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23059669' stroke-width='1'%3E%3Cpath d='M0 0h100v100H0z'/%3E%3Cpath d='M0 50h100M50 0v100'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-12 gap-12 lg:gap-16 items-center">
                {/* Left Content - 7 columns */}
                <div className="lg:col-span-7 text-center lg:text-left space-y-8">
                  {/* Floating Badge */}
                  <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/80 backdrop-blur-sm border border-green-200/50 shadow-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></div>
                    <span className="text-sm font-bold text-green-700 tracking-wide">Advanced Energy Assessment</span>
                  </div>

                  {/* Main Heading */}
                  <div className="space-y-4">
                    <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-black leading-tight">
                      <span className="text-gray-900 block">Comprehensive</span>
                      <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent block">
                        Energy Audits
                      </span>
                      <span className="text-gray-800 block">& Assessment</span>
                    </h1>

                    {/* Decorative Line */}
                    <div className="flex justify-center lg:justify-start">
                      <div className="w-24 h-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-lg lg:text-xl text-gray-800 leading-relaxed font-semibold max-w-2xl mx-auto lg:mx-0">
                    Transform your energy efficiency with our advanced audit platform designed for
                    <span className="text-green-700 font-black"> precise assessment</span>,
                    <span className="text-emerald-700 font-black"> regulatory compliance</span>, and
                    <span className="text-teal-700 font-black"> measurable results</span>
                  </p>

                  {/* Key Highlights */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-green-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-green-700" />
                      <span className="text-sm font-bold text-gray-800">15-30% Energy Savings</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-emerald-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-emerald-700" />
                      <span className="text-sm font-bold text-gray-800">99.9% Accuracy</span>
                    </div>
                    <div className="flex items-center space-x-2 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full border border-teal-200 shadow-md">
                      <CheckCircle className="w-4 h-4 text-teal-700" />
                      <span className="text-sm font-bold text-gray-800">100% Compliance</span>
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-4">
                    <button className="group bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-4 rounded-2xl font-bold text-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                      <Download className="w-5 h-5 mr-3 group-hover:animate-bounce" />
                      Get Audit Guide
                    </button>
                    <button className="group bg-white/80 backdrop-blur-sm text-green-700 px-8 py-4 rounded-2xl font-bold text-lg border-2 border-green-200 hover:border-green-400 hover:bg-white transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                      <Calendar className="w-5 h-5 mr-3 group-hover:animate-pulse" />
                      Schedule Audit
                    </button>
                  </div>
                </div>

                {/* Right Content - 5 columns */}
                <div className="lg:col-span-5 relative">
                  <div className="relative group">
                    {/* Main Image */}
                    <div className="relative overflow-hidden rounded-3xl shadow-2xl">
                      <img
                        src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&auto=format"
                        alt="Energy Audit Assessment"
                        className="w-full h-auto object-cover group-hover:scale-105 transition-all duration-700"
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-green-900/20 via-transparent to-transparent"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="py-16 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl lg:text-4xl font-black text-gray-900 mb-4">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Audit Performance
                </span>
              </h2>
              <p className="text-lg text-gray-800 font-bold max-w-2xl mx-auto">
                Proven results with our comprehensive energy audit platform
              </p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div key={index} className="group">
                    <div className="bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 border border-green-100 hover:border-green-300 transform hover:-translate-y-1">
                      <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-br ${stat.color} mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div className="text-2xl lg:text-3xl font-black text-gray-900 mb-2 group-hover:text-green-700 transition-colors">
                        {stat.value}
                      </div>
                      <div className="text-gray-800 font-black text-sm">{stat.label}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Why Choose Energy Audits Section */}
        <div className="py-20 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 relative">
          {/* Background Elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-200/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-200/20 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Why Choose Our
                </span>
                <br />
                <span className="text-gray-800">Energy Audit Platform?</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-800 max-w-4xl mx-auto font-bold leading-relaxed">
                Advanced assessment tools to optimize energy efficiency and ensure
                <span className="text-green-700 font-black"> regulatory compliance</span>
              </p>
            </div>
          </div>
        </div>

        {/* Key Benefits Section */}
        <div className="py-20 bg-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Key Benefits
                </span>
                <br />
                <span className="text-gray-800">of Our Platform</span>
              </h2>
              <p className="text-xl lg:text-2xl text-gray-800 max-w-4xl mx-auto font-bold leading-relaxed">
                Experience comprehensive energy audit capabilities with
                <span className="text-green-700 font-black"> advanced technology</span>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className="group"
                  onMouseEnter={() => setHoveredBenefit(index)}
                  onMouseLeave={() => setHoveredBenefit(null)}
                >
                  <div className="bg-white/90 backdrop-blur-sm border border-green-100 rounded-2xl p-8 hover:shadow-2xl hover:border-green-300 transition-all duration-500 transform hover:-translate-y-2">
                    <div className={`inline-flex p-3 rounded-xl mb-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white transform transition-transform duration-300 shadow-lg ${hoveredBenefit === index ? 'scale-110 rotate-3' : ''}`}>
                      {benefit.icon}
                    </div>
                    <h3 className="text-xl font-black text-gray-900 mb-4 group-hover:text-green-700 transition-colors">
                      {benefit.title}
                    </h3>
                    <p className="text-gray-800 leading-relaxed font-bold">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Compact CTA Section */}
        <div className="py-12 bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 relative overflow-hidden">
          {/* Subtle Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          {/* Floating Elements */}
          <div className="absolute top-4 left-10 w-16 h-16 bg-white/10 rounded-full blur-lg animate-pulse"></div>
          <div className="absolute bottom-4 right-10 w-20 h-20 bg-emerald-300/10 rounded-full blur-xl animate-pulse delay-700"></div>

          <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-2xl">
              <div className="grid lg:grid-cols-2 gap-8 items-center">
                {/* Left Content */}
                <div className="text-center lg:text-left">
                  <h2 className="text-2xl lg:text-3xl font-black text-white mb-3">
                    Ready to <span className="text-green-200">Optimize</span> Your Energy Efficiency?
                  </h2>
                  <p className="text-green-100 font-bold mb-6 text-lg">
                    Join <span className="text-white font-black">500+</span> organizations achieving
                    <span className="text-green-200 font-black"> comprehensive energy audits</span>
                  </p>

                  {/* Quick Stats */}
                  <div className="flex flex-wrap gap-4 justify-center lg:justify-start mb-6">
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">15-30%</div>
                      <div className="text-green-200 text-xs">Energy Savings</div>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">99.9%</div>
                      <div className="text-green-200 text-xs">Accuracy</div>
                    </div>
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                      <div className="text-white font-bold text-sm">100%</div>
                      <div className="text-green-200 text-xs">Compliance</div>
                    </div>
                  </div>
                </div>

                {/* Right Content - Action Buttons */}
                <div className="flex flex-col sm:flex-row lg:flex-col gap-4">
                  <button className="group bg-white text-green-700 px-6 py-3 rounded-xl font-bold text-lg hover:bg-green-50 transition-all duration-300 flex items-center justify-center shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                    <Download className="w-5 h-5 mr-3 group-hover:animate-pulse" />
                    Start Energy Audit
                  </button>
                  <button className="group border-2 border-white text-white px-6 py-3 rounded-xl font-bold text-lg hover:bg-white hover:text-green-700 transition-all duration-300 flex items-center justify-center backdrop-blur-sm transform hover:-translate-y-1">
                    <Calendar className="w-5 h-5 mr-3 group-hover:animate-bounce" />
                    Schedule Consultation
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    </div>
  );
};

export default EnergyAuditsPage;